/**
 * Environment variables utility for React Native
 *
 * This file provides type-safe access to environment variables
 * and ensures they are properly loaded from .env files.
 *
 * Note: Firebase configuration is NOT included here because @react-native-firebase
 * automatically reads configuration from Google Services files:
 * - Android: android/app/google-services.json
 * - iOS: ios/mobile/GoogleService-Info.plist
 */

import {
  API_URL,
  WS_URL,
  ENV,
  DEBUG,
  BACKEND_PORT,
  LOCAL_IP,
  LOG_LEVEL,
  ENABLE_LOGGING,
} from '@env';

// Define the shape of our environment variables
interface EnvVariables {
  API_URL: string;
  WS_URL: string;
  ENV: 'development' | 'staging' | 'production';
  DEBUG: boolean;
  BACKEND_PORT: string;
  LOCAL_IP: string;
  LOG_LEVEL: string;
  ENABLE_LOGGING: boolean;
}

// Get environment variables with type safety
// Development mode (__DEV__) uses .env file values
// Production mode uses cloudflare URLs
export const env: EnvVariables = {
  API_URL: __DEV__ ? API_URL : 'https://tough-disc-merit-median.trycloudflare.com/',
  WS_URL: __DEV__ ? WS_URL : 'wss://tough-disc-merit-median.trycloudflare.com/',
  ENV: (ENV as 'development' | 'staging' | 'production') || (__DEV__ ? 'development' : 'production'),
  DEBUG: DEBUG === 'true' || __DEV__,
  BACKEND_PORT: BACKEND_PORT || '3002',
  LOCAL_IP: LOCAL_IP || '***********',
  LOG_LEVEL: LOG_LEVEL || 'debug',
  ENABLE_LOGGING: ENABLE_LOGGING === 'true' || __DEV__,
};

// Helper functions
export const isDevelopment = (): boolean => env.ENV === 'development';
export const isStaging = (): boolean => env.ENV === 'staging';
export const isProduction = (): boolean => env.ENV === 'production';
export const isDebugMode = (): boolean => env.DEBUG;

// API URL helpers
export const getApiUrl = (): string => env.API_URL;
export const getWsUrl = (): string => env.WS_URL;

// Conditional logging that only works in development/debug mode
export const debugLog = (...args: any[]): void => {
  if (isDebugMode()) {
    console.log('[DEBUG]', ...args);
  }
};

export default env;
