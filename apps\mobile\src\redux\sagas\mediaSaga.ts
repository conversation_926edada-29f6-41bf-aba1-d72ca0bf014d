import { call, put, takeLatest, select } from 'redux-saga/effects';
import { PayloadAction } from '@reduxjs/toolkit';
import { apiService, MediaUploadResponse } from '../../services/api';
import { selectAuthToken } from '../slices/authSlice';
import { uploadStart, uploadProgress, uploadSuccess, uploadFailure } from '../slices/mediaSlice';

// Action types
export const UPLOAD_MEDIA_REQUEST = 'media/uploadRequest';

// Action creators
export const uploadMediaRequest = (file: any) => ({
  type: UPLOAD_MEDIA_REQUEST,
  payload: { file }
});

// Media upload saga using direct upload
function* uploadMediaSaga(action: PayloadAction<{
  file: any;
}>): Generator<any, void, any> {
  try {
    const { file } = action.payload;
    
    // Get auth token from Redux store
    const token: string = yield select(selectAuthToken);
    if (!token) {
      throw new Error('No authentication token available');
    }

    // Set auth token in API service
    apiService.setAuthToken(token);

    console.log('Starting media upload:', file.name || file.uri);

    // Dispatch upload start
    yield put(uploadStart());

    // Upload media using the direct upload method with progress tracking
    const mediaData: MediaUploadResponse = yield call(
      apiService.uploadMedia,
      file,
      function* (progress: number) {
        yield put(uploadProgress(progress));
      }
    );

    console.log('Media upload successful:', mediaData);

    // Dispatch success action
    yield put(uploadSuccess(mediaData));

  } catch (error: any) {
    console.error('Media upload failed:', error);
    
    const errorMessage = error.response?.data?.message || 
                        error.message || 
                        'Failed to upload media';
    
    yield put(uploadFailure(errorMessage));
  }
}

// Media saga watcher
export function* mediaSaga() {
  yield takeLatest(UPLOAD_MEDIA_REQUEST, uploadMediaSaga);
}

// Export saga for root saga
export default mediaSaga; 